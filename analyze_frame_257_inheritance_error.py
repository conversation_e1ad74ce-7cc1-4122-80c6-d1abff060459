#!/usr/bin/env python3
"""
分析frame_00257.jpg继承机制和流程机制错误的专用脚本

根据测试素材详细介绍.md文档：
- frame_00256.jpg: 7区域出现卡牌2叁
- frame_00257.jpg: 9区域出现卡牌2叁 流转继承上一帧7状态区域 2叁
- frame_00258.jpg: 9区域出现卡牌2叁 流转继承上一帧9状态区域 2叁
- frame_00259.jpg: 9区域出现卡牌2叁 流转继承上一帧9状态区域 2叁

问题：frame_00257.jpg及后续帧中错误分配或继承状态9
"""

import json
import sys
from pathlib import Path

def load_frame_data(frame_name):
    """加载指定帧的JSON数据"""
    json_path = Path(f"output/calibration_gt_final_with_digital_twin/labels/{frame_name}.json")
    if not json_path.exists():
        print(f"❌ 文件不存在: {json_path}")
        return None
    
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_cards_by_region(data, region_id):
    """提取指定区域的所有卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    cards = []
    for shape in data['shapes']:
        if shape.get('group_id') == region_id:
            card_info = {
                'label': shape.get('label', ''),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'points': shape.get('points', []),
                'score': shape.get('score'),
                'region_name': shape.get('region_name', ''),
                'owner': shape.get('owner', '')
            }
            cards.append(card_info)
    return cards

def analyze_san_card_flow():
    """分析"叁"卡牌的流转过程"""
    print("🔍 分析frame_00256-00259中'叁'卡牌的流转过程")
    print("=" * 80)
    
    frames = ['frame_00256', 'frame_00257', 'frame_00258', 'frame_00259']
    
    for frame_name in frames:
        print(f"\n📋 {frame_name}.jpg 分析:")
        print("-" * 40)
        
        data = load_frame_data(frame_name)
        if not data:
            continue
        
        # 查找所有包含"叁"的卡牌
        san_cards = []
        for shape in data.get('shapes', []):
            label = shape.get('label', '')
            if '叁' in label:
                card_info = {
                    'label': label,
                    'group_id': shape.get('group_id'),
                    'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                    'region_name': shape.get('region_name', ''),
                    'points': shape.get('points', [])
                }
                san_cards.append(card_info)
        
        if san_cards:
            for i, card in enumerate(san_cards):
                print(f"  叁卡牌{i+1}: 标签='{card['label']}', 区域={card['group_id']}, "
                      f"数字孪生ID='{card['digital_twin_id']}', 区域名='{card['region_name']}'")
        else:
            print("  ❌ 未发现'叁'卡牌")
        
        # 分析区域7和区域9的所有卡牌
        region_7_cards = extract_cards_by_region(data, 7)
        region_9_cards = extract_cards_by_region(data, 9)
        
        if region_7_cards:
            print(f"  区域7卡牌数: {len(region_7_cards)}")
            for card in region_7_cards:
                print(f"    - {card['label']} (ID: {card['digital_twin_id']})")
        
        if region_9_cards:
            print(f"  区域9卡牌数: {len(region_9_cards)}")
            for card in region_9_cards:
                print(f"    - {card['label']} (ID: {card['digital_twin_id']})")

def analyze_inheritance_logic():
    """分析继承逻辑的问题"""
    print("\n\n🔧 继承逻辑问题分析")
    print("=" * 80)
    
    # 加载关键帧数据
    frame_256 = load_frame_data('frame_00256')
    frame_257 = load_frame_data('frame_00257')
    frame_258 = load_frame_data('frame_00258')
    frame_259 = load_frame_data('frame_00259')
    
    if not all([frame_256, frame_257, frame_258, frame_259]):
        print("❌ 无法加载所有必要的帧数据")
        return
    
    print("\n📊 预期行为 vs 实际输出对比:")
    print("-" * 50)
    
    # 分析frame_00256 -> frame_00257的流转
    print("\n🔄 frame_00256 → frame_00257 流转分析:")
    
    # 查找frame_00256中区域7的"叁"卡牌
    region_7_san_256 = None
    for shape in frame_256.get('shapes', []):
        if shape.get('group_id') == 7 and '叁' in shape.get('label', ''):
            region_7_san_256 = {
                'label': shape.get('label'),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'points': shape.get('points', [])
            }
            break
    
    # 查找frame_00257中区域9的"叁"卡牌
    region_9_san_257 = None
    for shape in frame_257.get('shapes', []):
        if shape.get('group_id') == 9 and '叁' in shape.get('label', ''):
            region_9_san_257 = {
                'label': shape.get('label'),
                'digital_twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'points': shape.get('points', [])
            }
            break
    
    if region_7_san_256:
        print(f"  frame_00256区域7: {region_7_san_256['label']} (ID: {region_7_san_256['digital_twin_id']})")
    else:
        print("  ❌ frame_00256区域7未发现'叁'卡牌")
    
    if region_9_san_257:
        print(f"  frame_00257区域9: {region_9_san_257['label']} (ID: {region_9_san_257['digital_twin_id']})")
    else:
        print("  ❌ frame_00257区域9未发现'叁'卡牌")
    
    # 检查继承是否正确
    if region_7_san_256 and region_9_san_257:
        expected_id = region_7_san_256['digital_twin_id']
        actual_id = region_9_san_257['digital_twin_id']
        
        print(f"\n📋 继承检查:")
        print(f"  预期ID: {expected_id}")
        print(f"  实际ID: {actual_id}")
        
        if expected_id == actual_id:
            print("  ✅ 继承正确")
        else:
            print("  ❌ 继承错误！")
            print(f"  🔍 错误类型: 应该继承'{expected_id}'，但实际分配了'{actual_id}'")

def analyze_region_transition_rules():
    """分析区域流转规则的实现"""
    print("\n\n⚙️ 区域流转规则分析")
    print("=" * 80)
    
    print("📋 根据GAME_RULES.md，7→9流转规则:")
    print("  - 7状态的牌，如果观战方用不上，进入9状态弃牌区")
    print("  - 应该保持相同的数字孪生ID")
    print("  - 继承优先，新增补充")
    
    print("\n🔍 当前实现问题分析:")
    print("  1. 继承机制可能没有正确处理7→9流转")
    print("  2. ID分配器可能重新分配了ID而不是继承")
    print("  3. 跨区域继承规则可能有优先级问题")

def main():
    """主函数"""
    print("🔍 Frame_00257继承机制和流程机制错误深度分析")
    print("=" * 80)
    print("基于测试素材详细介绍.md文档的预期行为分析")
    
    # 分析"叁"卡牌的流转过程
    analyze_san_card_flow()
    
    # 分析继承逻辑问题
    analyze_inheritance_logic()
    
    # 分析区域流转规则
    analyze_region_transition_rules()
    
    print("\n\n📊 总结")
    print("=" * 80)
    print("根据分析，frame_00257.jpg的问题可能出现在以下方面：")
    print("1. 🔧 7→9区域流转的继承机制未正确实现")
    print("2. 🔧 ID分配器覆盖了继承结果")
    print("3. 🔧 跨区域继承优先级设置错误")
    print("4. 🔧 区域9的继承逻辑存在缺陷")
    
    print("\n建议修复方向：")
    print("- 检查simple_inheritor.py中9←7的继承规则")
    print("- 检查region_transitioner.py中7→9流转处理")
    print("- 检查basic_id_assigner.py中继承ID的注册逻辑")
    print("- 验证区域9的位置稳定性继承算法")

if __name__ == "__main__":
    main()

"""
第21张牌跟踪器 (Card21Tracker) - 增强版
单一职责：跟踪第21张牌并实现庄家身份判断和区域17动态管理

核心功能：
1. 动态判断庄家身份（基于第21张牌首次出现位置）
2. 根据庄家身份动态启用/禁用区域17
3. 虚拟投射管理（对战方庄家第21张牌消失时）
4. 第21张牌跟踪和重现检测
5. 单局边界管理（小结算画面重置）
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class Card21TrackingResult:
    """第21张牌跟踪结果"""
    tracked_cards: Dict[str, Any]  # 当前跟踪的第21张牌信息
    reappeared_cards: List[Dict[str, Any]]  # 重新出现的卡牌
    virtual_projections: List[Dict[str, Any]]  # 虚拟投射卡牌
    dealer_identity: Optional[str]  # 庄家身份：'spectator'/'opponent'/None
    region_17_enabled: bool  # 区域17是否启用
    statistics: Dict[str, Any]

class Card21Tracker:
    """第21张牌跟踪器 - 增强版：庄家判断+区域17管理+虚拟投射"""

    def __init__(self):
        # 第21张牌跟踪记录：{标签: 跟踪信息}
        self.tracked_cards: Dict[str, Dict[str, Any]] = {}

        # 🆕 庄家身份判断系统
        self.dealer_identity: Optional[str] = None  # 'spectator'/'opponent'/None
        self.game_started = False  # 游戏是否已开始（观战方手牌达到20张）
        self.dealer_determined = False  # 庄家身份是否已确定
        self.card_21_first_appearance = None  # 第21张牌首次出现的区域

        # 🆕 区域17管理系统 - 一次性投射模式
        self.region_17_enabled = False  # 区域17是否启用
        self.initial_projection_completed = False  # 是否已完成初始投射
        self.projected_card_info = None  # 已投射的卡牌信息

        # 🆕 单局边界管理
        self.game_phase = 'waiting'  # 'waiting'/'playing'/'settlement'
        self.settlement_keywords = {'你赢了', '你输了', '荒庄'}

        # 跟踪统计
        self.tracking_stats = {
            "total_frames": 0,
            "cards_disappeared": 0,
            "cards_reappeared": 0,
            "currently_tracking": 0,
            "initial_projections_created": 0,  # 改名：初始投射创建次数
            "dealer_determinations": 0,
            "game_resets": 0,
            "ai_inference_support": 0
        }

        # 区域定义
        self.central_regions = [7]  # 对战方抓牌区
        self.spectator_draw_regions = [3]  # 观战方抓牌区
        self.discard_regions = [8, 16]  # 对战方打牌区和吃碰区

        logger.info("增强版第21张牌跟踪器初始化完成 - 支持庄家判断和区域17管理")
    
    def process_card21_tracking(self, current_cards: List[Dict[str, Any]], 
                               previous_cards: List[Dict[str, Any]] = None,
                               game_context: Dict[str, Any] = None) -> Card21TrackingResult:
        """
        处理第21张牌跟踪
        
        Args:
            current_cards: 当前帧卡牌列表
            previous_cards: 前一帧卡牌列表
            game_context: 游戏上下文信息
            
        Returns:
            跟踪结果
        """
        self.tracking_stats["total_frames"] += 1

        # 🆕 步骤1：检测单局边界（小结算画面）
        if self._detect_settlement_boundary(current_cards):
            logger.info("🔄 检测到小结算画面，重置庄家判断和区域17状态")
            self._reset_game_state()
            self.tracking_stats["game_resets"] += 1

        # 🆕 步骤2：庄家身份判断（仅在游戏开始后且未确定时执行）
        if not self.dealer_determined:
            self._detect_dealer_identity(current_cards, previous_cards)

        # 🆕 步骤3：区域17启用状态管理
        self._update_region_17_status()

        # 🆕 步骤4：虚拟投射处理
        virtual_projections = []
        if self.region_17_enabled and previous_cards:
            if not self.initial_projection_completed:
                # 首次创建虚拟投射
                virtual_projections = self._handle_initial_projection(current_cards, previous_cards)
            elif self.projected_card_info:
                # 后续帧：输出已跟踪的虚拟投射卡牌
                virtual_projections = [self.projected_card_info.copy()]
                logger.debug(f"🔮 输出已跟踪的虚拟投射: {self.projected_card_info.get('label', 'None')}")

        # 步骤5：传统第21张牌跟踪
        if previous_cards:
            self._detect_disappearance_events(current_cards, previous_cards)

        # 步骤6：重现事件检测
        reappeared_cards = self._detect_reappearance_events(current_cards)

        # 步骤7：更新跟踪状态
        self._update_tracking_status()

        logger.info(f"增强版第21张牌跟踪: 庄家={self.dealer_identity}, 区域17={self.region_17_enabled}, "
                   f"初始投射完成={self.initial_projection_completed}, 跟踪{len(self.tracked_cards)}张, "
                   f"本帧虚拟投射{len(virtual_projections)}张, 重现{len(reappeared_cards)}张")

        return Card21TrackingResult(
            tracked_cards=self.tracked_cards.copy(),
            reappeared_cards=reappeared_cards,
            virtual_projections=virtual_projections,
            dealer_identity=self.dealer_identity,
            region_17_enabled=self.region_17_enabled,
            statistics=self._generate_statistics()
        )

    def _detect_settlement_boundary(self, current_cards: List[Dict[str, Any]]) -> bool:
        """检测小结算画面边界"""
        for card in current_cards:
            label = card.get('label', '')
            if any(keyword in label for keyword in self.settlement_keywords):
                return True
        return False

    def _reset_game_state(self):
        """重置游戏状态（单局结束时调用）"""
        self.dealer_identity = None
        self.dealer_determined = False
        self.game_started = False
        self.card_21_first_appearance = None
        self.region_17_enabled = False
        self.initial_projection_completed = False  # 重置投射状态
        self.projected_card_info = None  # 清空投射信息
        self.tracked_cards.clear()
        self.game_phase = 'waiting'
        logger.info("游戏状态已重置，等待下一局开始（一次性投射模式）")

    def _detect_dealer_identity(self, current_cards: List[Dict[str, Any]],
                               previous_cards: List[Dict[str, Any]] = None):
        """
        检测庄家身份

        判断规则：
        - 第21张牌首次出现在区域7 → 对战方是庄家
        - 第21张牌首次出现在区域3 → 观战方是庄家
        """
        if self.dealer_determined or not previous_cards:
            return

        # 检测游戏是否开始（观战方手牌达到20张）
        spectator_hand_cards = [card for card in current_cards
                               if card.get('group_id') == 1]

        if len(spectator_hand_cards) >= 20:
            self.game_started = True

        if not self.game_started:
            return

        # 检测第21张牌首次出现位置
        # 检查区域7（对战方抓牌区）
        curr_region_7 = [card for card in current_cards
                        if card.get('group_id') == 7]
        prev_region_7 = [card for card in previous_cards
                        if card.get('group_id') == 7]

        # 检查区域3（观战方抓牌区）
        curr_region_3 = [card for card in current_cards
                        if card.get('group_id') == 3]
        prev_region_3 = [card for card in previous_cards
                        if card.get('group_id') == 3]

        # 检测区域7新出现的卡牌（对战方抓牌）
        if len(curr_region_7) > len(prev_region_7):
            self.dealer_identity = 'opponent'
            self.dealer_determined = True
            self.card_21_first_appearance = 'region_7'
            self.tracking_stats["dealer_determinations"] += 1
            logger.info("🎯 庄家身份确定：对战方是庄家（第21张牌出现在区域7）")
            return

        # 检测区域3新出现的卡牌（观战方抓牌）
        if len(curr_region_3) > len(prev_region_3):
            self.dealer_identity = 'spectator'
            self.dealer_determined = True
            self.card_21_first_appearance = 'region_3'
            self.tracking_stats["dealer_determinations"] += 1
            logger.info("🎯 庄家身份确定：观战方是庄家（第21张牌出现在区域3）")
            return

    def _update_region_17_status(self):
        """更新区域17启用状态"""
        if self.dealer_identity == 'opponent':
            if not self.region_17_enabled:
                self.region_17_enabled = True
                logger.info("✅ 启用区域17（对战方是庄家）")
        elif self.dealer_identity == 'spectator':
            if self.region_17_enabled:
                self.region_17_enabled = False
                logger.info("❌ 禁用区域17（观战方是庄家）")

    def _handle_initial_projection(self, current_cards: List[Dict[str, Any]],
                                  previous_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理一次性初始投射（仅在区域17启用且未完成初始投射时）

        当对战方庄家的第21张牌从区域7消失时，在区域17创建一次性虚拟投射
        后续帧通过BasicIdAssigner的继承逻辑自动处理
        """
        if not self.region_17_enabled or self.initial_projection_completed:
            return []

        # 检测区域7卡牌消失
        prev_region_7 = [card for card in previous_cards if card.get('group_id') == 7]
        curr_region_7 = [card for card in current_cards if card.get('group_id') == 7]

        # 构建当前区域7标签集合
        curr_labels = {card.get('label') for card in curr_region_7}

        # 检测消失的卡牌（只处理第一个消失的卡牌）
        for prev_card in prev_region_7:
            label = prev_card.get('label')
            if label and label not in curr_labels:
                # 创建一次性虚拟投射到区域17
                virtual_card = self._create_initial_virtual_projection(prev_card)
                if virtual_card:
                    # 标记初始投射已完成
                    self.initial_projection_completed = True
                    self.projected_card_info = virtual_card.copy()
                    self.tracking_stats["initial_projections_created"] += 1

                    logger.info(f"🔮 创建一次性虚拟投射：{label} → 区域17 (后续通过继承系统处理)")
                    return [virtual_card]

        return []

    def _create_initial_virtual_projection(self, original_card: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建一次性初始虚拟投射卡牌，完全兼容BasicIdAssigner继承系统

        使用标准卡牌格式，确保后续帧能通过继承逻辑正常处理
        """
        label = original_card.get('label')

        # 确保数字孪生ID字段的一致性
        twin_id = original_card.get('digital_twin_id') or original_card.get('twin_id') or label

        # 创建AnyLabeling格式的shape（用于最终JSON输出）
        anylabeling_shape = {
            "label": twin_id,  # 使用数字孪生ID作为标签
            "points": [[520.0, 180.0], [530.0, 180.0], [530.0, 200.0], [520.0, 200.0]],
            "group_id": 17,
            "shape_type": "rectangle",
            "flags": {},
            "description": "",
            "kie_linking": [],
            "difficult": False,
            "region_name": "虚拟投射区_对战方第21张牌",
            "owner": "opponent",
            "score": original_card.get('score', 1.0),
            "attributes": {
                "digital_twin_id": twin_id,
                "is_virtual": True,
                "virtual_projection": True
            }
        }

        # 创建标准格式的虚拟投射卡牌
        virtual_card = {
            # 标准卡牌字段
            "label": label,
            "digital_twin_id": twin_id,
            "group_id": 17,  # 17区域
            "region_name": "虚拟投射区_对战方第21张牌",
            "owner": "opponent",

            # 虚拟坐标（固定位置）
            "points": [[520.0, 180.0], [530.0, 180.0], [530.0, 200.0], [520.0, 200.0]],
            "bbox": [520.0, 180.0, 530.0, 200.0],

            # 继承系统兼容标记
            "is_virtual": True,  # 标记为虚拟卡牌
            "virtual_projection": True,  # Card21Tracker创建的投射
            "original_region": original_card.get('group_id', 7),  # 记录原始区域

            # 继承系统需要的标准字段
            "score": original_card.get('score', 1.0),
            "shape_type": "rectangle",
            "flags": {},
            "attributes": {
                "digital_twin_id": twin_id,
                "is_virtual": True,
                "processing_version": "card21_tracker_v2.0"
            },

            # 🆕 添加AnyLabeling格式信息，用于最终JSON输出
            "anylabeling_shape": anylabeling_shape
        }

        logger.info(f"🔮 创建一次性虚拟投射：标签'{label}', ID'{twin_id}', "
                   f"区域{original_card.get('group_id')}→17, 后续通过继承系统处理")

        return virtual_card



    def _detect_disappearance_events(self, current_cards: List[Dict[str, Any]],
                                   previous_cards: List[Dict[str, Any]]):
        """检测中央区域卡牌消失事件"""
        # 获取前一帧中央区域的卡牌
        prev_central_cards = [
            card for card in previous_cards 
            if card.get('group_id') in self.central_regions
        ]
        
        # 获取当前帧中央区域的卡牌
        current_central_cards = [
            card for card in current_cards 
            if card.get('group_id') in self.central_regions
        ]
        
        # 构建当前帧标签集合
        current_labels = {card.get('label') for card in current_central_cards}
        
        # 检测消失的卡牌
        for prev_card in prev_central_cards:
            label = prev_card.get('label')
            if label and label not in current_labels:
                # 记录消失事件
                self._record_disappearance(label, prev_card)
    
    def _record_disappearance(self, label: str, card_info: Dict[str, Any]):
        """记录卡牌消失事件"""
        if label not in self.tracked_cards:
            tracking_info = {
                'label': label,
                'disappeared_frame': self.tracking_stats["total_frames"],
                'original_card_info': card_info.copy(),
                'status': 'tracking',
                'ai_inference_value': True  # 对AI推理有价值
            }
            
            self.tracked_cards[label] = tracking_info
            self.tracking_stats["cards_disappeared"] += 1
            
            logger.info(f"记录第21张牌消失: {label} (帧{self.tracking_stats['total_frames']})")
    
    def _detect_reappearance_events(self, current_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检测打牌区域重现事件"""
        reappeared_cards = []
        
        # 获取当前帧打牌区域的卡牌
        discard_cards = [
            card for card in current_cards 
            if card.get('group_id') in self.discard_regions
        ]
        
        # 检查是否有跟踪的卡牌重现
        for card in discard_cards:
            label = card.get('label')
            if label in self.tracked_cards:
                # 卡牌重现，停止跟踪
                tracking_info = self.tracked_cards[label]
                tracking_info['status'] = 'reappeared'
                tracking_info['reappeared_frame'] = self.tracking_stats["total_frames"]
                tracking_info['reappeared_card_info'] = card.copy()
                
                reappeared_cards.append({
                    'label': label,
                    'tracking_info': tracking_info,
                    'current_card': card
                })
                
                # 从跟踪列表中移除
                del self.tracked_cards[label]
                self.tracking_stats["cards_reappeared"] += 1
                
                logger.info(f"第21张牌重现: {label} (帧{self.tracking_stats['total_frames']})")
        
        return reappeared_cards
    
    def _update_tracking_status(self):
        """更新跟踪状态"""
        self.tracking_stats["currently_tracking"] = len(self.tracked_cards)
    
    def _generate_ai_support_info(self) -> Dict[str, Any]:
        """生成AI推理支持信息"""
        support_info = {
            'known_opponent_cards': list(self.tracked_cards.keys()),
            'uncertainty_reduction': len(self.tracked_cards),  # 减少的不确定性
            'total_unknown_cards': 80 - len(self.tracked_cards),  # 剩余未知卡牌数
            'inference_confidence_boost': min(len(self.tracked_cards) * 0.05, 0.2)  # 推理置信度提升
        }
        
        if self.tracked_cards:
            self.tracking_stats["ai_inference_support"] += 1
        
        return support_info
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成增强版统计信息"""
        return {
            "total_frames": self.tracking_stats["total_frames"],
            "cards_disappeared": self.tracking_stats["cards_disappeared"],
            "cards_reappeared": self.tracking_stats["cards_reappeared"],
            "currently_tracking": self.tracking_stats["currently_tracking"],
            "initial_projections_created": self.tracking_stats["initial_projections_created"],
            "dealer_determinations": self.tracking_stats["dealer_determinations"],
            "game_resets": self.tracking_stats["game_resets"],
            "ai_inference_support": self.tracking_stats["ai_inference_support"],
            "tracking_efficiency": self._calculate_tracking_efficiency(),
            "dealer_identity": self.dealer_identity,
            "region_17_enabled": self.region_17_enabled,
            "initial_projection_completed": self.initial_projection_completed
        }
    
    def _calculate_tracking_efficiency(self) -> float:
        """计算跟踪效率"""
        if self.tracking_stats["cards_disappeared"] == 0:
            return 0.0
        return self.tracking_stats["cards_reappeared"] / self.tracking_stats["cards_disappeared"]
    
    def get_ai_inference_support(self) -> Dict[str, Any]:
        """获取AI推理支持信息"""
        return self._generate_ai_support_info()
    
    def reset_tracking(self):
        """重置跟踪状态（完全重置）"""
        self.tracked_cards.clear()
        self.dealer_identity = None
        self.dealer_determined = False
        self.game_started = False
        self.card_21_first_appearance = None
        self.region_17_enabled = False
        self.initial_projection_completed = False  # 重置投射状态
        self.projected_card_info = None  # 清空投射信息
        self.game_phase = 'waiting'

        self.tracking_stats = {
            "total_frames": 0,
            "cards_disappeared": 0,
            "cards_reappeared": 0,
            "currently_tracking": 0,
            "initial_projections_created": 0,  # 更新字段名
            "dealer_determinations": 0,
            "game_resets": 0,
            "ai_inference_support": 0
        }
        logger.info("增强版第21张牌跟踪器状态已完全重置（一次性投射模式）")

    def reset_projection_state(self):
        """重置投射状态（用于流转检测后的状态重置）"""
        self.initial_projection_completed = False
        self.projected_card_info = None
        logger.info("🔄 重置投射状态，准备下次投射")

def create_card21_tracker():
    """创建第21张牌跟踪器"""
    return Card21Tracker()

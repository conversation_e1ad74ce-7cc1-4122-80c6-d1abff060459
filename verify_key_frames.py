#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def verify_frame_298():
    """验证frame_00298的捌卡牌顺序"""
    print("=== frame_00298.json 捌卡牌验证 ===")
    
    with open('output/calibration_gt_final_with_digital_twin/labels/frame_00298.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    ba_cards = []
    for shape in data['shapes']:
        twin_id = shape['attributes'].get('digital_twin_id', 'None')
        if '捌' in twin_id:
            region = shape.get('group_id', 0)
            points = shape['points']
            y_center = (points[0][1] + points[2][1]) / 2
            ba_cards.append({
                'twin_id': twin_id,
                'region': region,
                'y_center': y_center
            })

    # 按区域分组并排序
    ba_by_region = {}
    for card in ba_cards:
        region = card['region']
        if region not in ba_by_region:
            ba_by_region[region] = []
        ba_by_region[region].append(card)

    for region in sorted(ba_by_region.keys()):
        cards = sorted(ba_by_region[region], key=lambda x: -x['y_center'])
        print(f'区域{region}（{len(cards)}张）:')
        for card in cards:
            print(f'  {card["twin_id"]} - Y:{card["y_center"]:.1f}')

def verify_frame_274():
    """验证frame_00274的九卡牌"""
    print("\n=== frame_00274.json 九卡牌验证 ===")
    
    with open('output/calibration_gt_final_with_digital_twin/labels/frame_00274.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    jiu_cards = []
    for shape in data['shapes']:
        twin_id = shape['attributes'].get('digital_twin_id', 'None')
        if '九' in twin_id:
            region = shape.get('group_id', 0)
            points = shape['points']
            y_center = (points[0][1] + points[2][1]) / 2
            jiu_cards.append({
                'twin_id': twin_id,
                'region': region,
                'y_center': y_center
            })

    # 按区域分组并排序
    jiu_by_region = {}
    for card in jiu_cards:
        region = card['region']
        if region not in jiu_by_region:
            jiu_by_region[region] = []
        jiu_by_region[region].append(card)

    for region in sorted(jiu_by_region.keys()):
        cards = sorted(jiu_by_region[region], key=lambda x: -x['y_center'])
        print(f'区域{region}（{len(cards)}张）:')
        for card in cards:
            print(f'  {card["twin_id"]} - Y:{card["y_center"]:.1f}')

def verify_total_cards():
    """验证总卡牌数"""
    print("\n=== 总卡牌数验证 ===")
    
    test_frames = ['frame_00124.json', 'frame_00274.json', 'frame_00298.json', 'frame_00341.json']
    
    for frame in test_frames:
        try:
            with open(f'output/calibration_gt_final_with_digital_twin/labels/{frame}', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            twin_ids = []
            for shape in data['shapes']:
                twin_id = shape['attributes'].get('digital_twin_id')
                if twin_id:
                    twin_ids.append(twin_id)
            
            print(f'{frame}: {len(twin_ids)}张卡牌有数字孪生ID')
            
        except Exception as e:
            print(f'{frame}: 错误 - {e}')

if __name__ == '__main__':
    verify_frame_298()
    verify_frame_274()
    verify_total_cards()
